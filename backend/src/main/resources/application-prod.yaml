spring:
  resources:
    staticLocations:
      - classpath:/static/
  datasource:
    url: ********************************************************************************************
    username: areaclienti2020
    password: C0MiT1$$9
    driver-class-name: net.sourceforge.jtds.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.SQLServerDialect
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
    show-sql: true
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.SQLServer2012Dialect
  servlet:
      multipart:
        max-file-size: 10MB
        max-request-size: 10MB
server:
  tomcat:
    max-swallow-size: -1
chat:
  ccmm:
    machine:
      address: ***********

optima:
  security:
      token:
        secret: aqP4muol5oJs82bSzOJl9Tfk39QN10sLCqV3PhGulqT0vltrafAGGyBgkCwwPcEFruBABBFvBL2u
      permitted-resources:
        - '/'
        - '/*.**'
        - '/assets/**'
        - '/home/<USER>'
        - '/gestione-condomini/**'
        - '/login/**'
        - '/profile/**'
        - '/invoices/**'
        - '/faidate/**'
        - '/support/**'
        - '/moduli/**'
        - '/isAdmin'
        - '/assistant'
        - '/product/**'
        - '/passa-tutto-in-uno/**'
        - '/api/mobile/product/description'
        - '/api/android/**'
        - '/api/mobile/tarifferInternazionali'
        - '/api/mobile/getTariffeNazionali'
        - '/short/data'
        - '/payment'
        - '/myoptima/**'
        - '/api/tariff/**'
        - '/api/otp/**'
        - '/api/mnp/**'
        - '/api/ip/**'
        - '/api/chat/**'
        - '/api/notifica/mdp/**'
        - '/api/notifications'
        - '/api/notifications/{clientId}'
        - '/api/checkIfUserAlreadyRestoredPassword'
        - '/api/getAllUsersWithTheSameEmail'
        - '/api/resetPassword/**'
        - '/api/nagios/**'
        - '/psw/setPassword'
        - '/psw/isExpired'
        - '/internal/authenticateUser'
        - '/forgotPassword/**'
        - '/internal/changeEmail'
        - '/internal/checkUsername'
        - '/internal/addNewCustomer'
        - '/api/voucher-card'
        - '/api/prospect/**'
        - '/api/prospect/information/**'
        - '/prospect/registration'
        - '/prospect/login'
        - '/api/optima-young/**'
        - '/api/health'
