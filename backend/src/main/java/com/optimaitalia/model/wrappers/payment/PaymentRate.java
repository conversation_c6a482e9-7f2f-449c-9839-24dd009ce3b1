package com.optimaitalia.model.wrappers.payment;

public class PaymentRate {

    private Long clientId;

    private StringBuilder rateNumberStr = new StringBuilder();
    private StringBuilder rateAmountStr = new StringBuilder();

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public StringBuilder getRateNumberStr() {
        return rateNumberStr;
    }

    public void setRateNumberStr(StringBuilder rateNumberStr) {
        this.rateNumberStr = rateNumberStr;
    }

    public StringBuilder getRateAmountStr() {
        return rateAmountStr;
    }

    public void setRateAmountStr(StringBuilder rateAmountStr) {
        this.rateAmountStr = rateAmountStr;
    }
}
