package com.optimaitalia.model.wrappers.sales;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class ListSales implements Serializable {

    private String idSales;
    private String salesTip;
    private String sale;
    private String phone;
    private String email;

    public String getIdSales() {
        return idSales;
    }
    @JsonProperty("Id Sales")
    public void setIdSales(String idSales) {
        this.idSales = idSales;
    }

    public String getSalesTipe() {
        return salesTip;
    }
    @JsonProperty("Tipo Sales")
    public void setSalesTipe(String salesTipe) {
        this.salesTip = salesTipe;
    }

    public String getSale() {
        return sale;
    }
    @JsonProperty("Sale")
    public void setSale(String sale) {
        this.sale = sale;
    }

    public String getPhone() {
        return phone;
    }
    @JsonProperty("Telefono")
    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }
    @JsonProperty("Email")
    public void setEmail(String email) {
        this.email = email;
    }
}
