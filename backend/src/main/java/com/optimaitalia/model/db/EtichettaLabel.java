package com.optimaitalia.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.sf.oval.constraint.NotEmpty;
import net.sf.oval.constraint.NotNull;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity
@Table(name = "etichetta_label")
public class EtichettaLabel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @NotNull
    @Column(name = "client_id", nullable = false)
    private Long clientId;

    @NotNull
    @Column(name = "pod", nullable = false)
    private String podUtNumber;

    @NotNull
    @NotEmpty
    @Column(name = "description", nullable = false)
    private String description;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Europe/Rome")
    @Column(name = "insert_date", nullable = false)
    private Date insertDate;

    @PrePersist
    private void onCreate() {
        this.insertDate = new Date();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof EtichettaLabel)) return false;
        EtichettaLabel that = (EtichettaLabel) o;
        return clientId.equals(that.clientId) && podUtNumber.equals(that.podUtNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientId, podUtNumber);
    }
}
