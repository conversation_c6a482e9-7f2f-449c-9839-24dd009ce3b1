package com.optimaitalia.model.db;


import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "tariff_data")
public class TariffData {

    @Id
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "zone")
    private String zone;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "sms")
    private Double sms;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "mms")
    private Double mms;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "call_connection")
    private Double callConnection;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "call_min")
    private Double callPerMin;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "videocall_connection")
    private Double videocallConnection;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "videocall_min")
    private Double videocallPerMin;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "data_mb")
    private Double dataPerMb;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "roaming_call_connection")
    private Double roamingCallConnection;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "zona_a_roaming_call")
    private Double roamingCallZoneA;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "zona_b_roaming_call")
    private Double roamingCallZoneB;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "zona_c_roaming_call")
    private Double roamingCallZoneC;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "zona_d_roaming_call")
    private Double roamingCallZoneD;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "roaming_sms")
    private Double roamingSms;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "roaming_mms")
    private Double roamingMms;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "call_from_abroad_connection")
    private Double callFromAbroadPerConnection;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "call_from_abroad_min")
    private Double callFromAbroadPerMin;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "sms_from_abroad")
    private Double smsFromAbroad;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Column(name = "mms_from_abroad")
    private Double mmsFromAbroad;


    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public Double getSms() {
        return sms;
    }

    public void setSms(Double sms) {
        this.sms = sms;
    }

    public Double getMms() {
        return mms;
    }

    public void setMms(Double mms) {
        this.mms = mms;
    }

    public Double getCallConnection() {
        return callConnection;
    }

    public void setCallConnection(Double callConnection) {
        this.callConnection = callConnection;
    }

    public Double getCallPerMin() {
        return callPerMin;
    }

    public void setCallPerMin(Double callPerMin) {
        this.callPerMin = callPerMin;
    }

    public Double getVideocallConnection() {
        return videocallConnection;
    }

    public void setVideocallConnection(Double videocallConnection) {
        this.videocallConnection = videocallConnection;
    }

    public Double getVideocallPerMin() {
        return videocallPerMin;
    }

    public void setVideocallPerMin(Double videocallPerMin) {
        this.videocallPerMin = videocallPerMin;
    }

    public Double getDataPerMb() {
        return dataPerMb;
    }

    public void setDataPerMb(Double dataPerMb) {
        this.dataPerMb = dataPerMb;
    }

    public Double getRoamingCallConnection() {
        return roamingCallConnection;
    }

    public void setRoamingCallConnection(Double roamingCallConnection) {
        this.roamingCallConnection = roamingCallConnection;
    }

    public Double getRoamingCallZoneA() {
        return roamingCallZoneA;
    }

    public void setRoamingCallZoneA(Double roamingCallZoneA) {
        this.roamingCallZoneA = roamingCallZoneA;
    }

    public Double getRoamingCallZoneB() {
        return roamingCallZoneB;
    }

    public void setRoamingCallZoneB(Double roamingCallZoneB) {
        this.roamingCallZoneB = roamingCallZoneB;
    }

    public Double getRoamingCallZoneC() {
        return roamingCallZoneC;
    }

    public void setRoamingCallZoneC(Double roamingCallZoneC) {
        this.roamingCallZoneC = roamingCallZoneC;
    }

    public Double getRoamingCallZoneD() {
        return roamingCallZoneD;
    }

    public void setRoamingCallZoneD(Double roamingCallZoneD) {
        this.roamingCallZoneD = roamingCallZoneD;
    }

    public Double getRoamingSms() {
        return roamingSms;
    }

    public void setRoamingSms(Double roamingSms) {
        this.roamingSms = roamingSms;
    }

    public Double getRoamingMms() {
        return roamingMms;
    }

    public void setRoamingMms(Double roamingMms) {
        this.roamingMms = roamingMms;
    }

    public Double getCallFromAbroadPerConnection() {
        return callFromAbroadPerConnection;
    }

    public void setCallFromAbroadPerConnection(Double callFromAbroadPerConnection) {
        this.callFromAbroadPerConnection = callFromAbroadPerConnection;
    }

    public Double getCallFromAbroadPerMin() {
        return callFromAbroadPerMin;
    }

    public void setCallFromAbroadPerMin(Double callFromAbroadPerMin) {
        this.callFromAbroadPerMin = callFromAbroadPerMin;
    }

    public Double getSmsFromAbroad() {
        return smsFromAbroad;
    }

    public void setSmsFromAbroad(Double smsFromAbroad) {
        this.smsFromAbroad = smsFromAbroad;
    }

    public Double getMmsFromAbroad() {
        return mmsFromAbroad;
    }

    public void setMmsFromAbroad(Double mmsFromAbroad) {
        this.mmsFromAbroad = mmsFromAbroad;
    }
}
