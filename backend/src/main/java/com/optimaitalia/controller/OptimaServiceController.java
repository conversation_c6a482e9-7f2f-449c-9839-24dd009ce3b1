package com.optimaitalia.controller;

import com.optima.security.model.changeCustomerEmail.ChangeEmailRequest;
import com.optima.security.model.changeCustomerEmail.ChangeEmailResponse;
import com.optima.security.model.newUserService.RequestNewUserService;
import com.optima.security.model.newUserService.ResponseNewUserService;
import com.optimaitalia.model.optimaResponseRequest.IsUserRequest;
import com.optimaitalia.model.optimaResponseRequest.IsUserResponse;
import com.optimaitalia.service.OptimaService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/internal")
public class OptimaServiceController {
    private final OptimaService optimaService;

    @Autowired
    public OptimaServiceController(OptimaService optimaService) {
        this.optimaService = optimaService;
    }
    private static final Logger logger = LogManager.getLogger(OptimaServiceController.class);

    @PostMapping("/checkUsername")
    public IsUserResponse isUserExist(@RequestBody IsUserRequest user) {
        logger.info("Checking if the user: " + user.getUsername() + " exists");
        return optimaService.isUserExist(user.getUsername());
    }
    @PostMapping("/addNewCustomer")
    public ResponseNewUserService addNewCustomer(@RequestBody RequestNewUserService user) {
        logger.info("Adding a new user "+user.getEmail());
        return optimaService.addNewUser(user);
    }
    @PostMapping("/changeEmail")
    public ChangeEmailResponse changeEmail(@RequestBody ChangeEmailRequest changeEmailRequest){
        logger.info("Changing email in user: " + changeEmailRequest.getUsername());
        return optimaService.changeEmail(changeEmailRequest);
    }
}
