package com.optima.chat.service;

import com.optima.chat.wsdl.utility.*;

public interface UtilityService {

    GetAnonymousSessionKeyResponse getAnonymousSessionKey();

    GetAndUpdateAnonymousCustomerIDResponse getAndUpdateAnonymousCustomerID(GetAndUpdateAnonymousCustomerID getAndUpdateAnonymousCustomerID);

    CustomerLogoffByContactIDResponse closeTextChat(CustomerLogoffByContactID request);

}
