package com.optima.chat.configuration;

import com.optima.chat.service.CommsService;
import com.optima.chat.service.CustomerService;
import com.optima.chat.service.SkillSetService;
import com.optima.chat.service.UtilityService;
import com.optima.chat.service.impl.CommsServiceImpl;
import com.optima.chat.service.impl.CustomerServiceImpl;
import com.optima.chat.service.impl.SkillSetServiceImpl;
import com.optima.chat.service.impl.UtilityServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;


@Configuration
@PropertySource("classpath:soap.properties")
public class CommonBeans {


    @Bean
    public Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPaths("com.optima.chat.wsdl.comms");
        return marshaller;
    }

    @Bean
    public Jaxb2Marshaller utilityMarshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPaths("com.optima.chat.wsdl.utility");
        return marshaller;
    }

    @Bean
    public Jaxb2Marshaller customerMarshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPaths("com.optima.chat.wsdl.customer");
        return marshaller;
    }

    @Bean
    public Jaxb2Marshaller skillMarshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPaths("com.optima.chat.wsdl.skill");
        return marshaller;
    }

    @Bean
    protected CommsService commsService() {
        CommsService commsService = new CommsServiceImpl();
        ((CommsServiceImpl) commsService).setMarshaller(this.marshaller());
        ((CommsServiceImpl) commsService).setUnmarshaller(this.marshaller());
        return commsService;
    }

    @Bean
    protected UtilityService utilityService() {
        UtilityService utilityService = new UtilityServiceImpl();
        ((UtilityServiceImpl) utilityService).setMarshaller(this.utilityMarshaller());
        ((UtilityServiceImpl) utilityService).setUnmarshaller(this.utilityMarshaller());
        return utilityService;
    }

    @Bean
    protected CustomerService customerService() {
        CustomerService customerService = new CustomerServiceImpl();
        ((CustomerServiceImpl) customerService).setMarshaller(this.customerMarshaller());
        ((CustomerServiceImpl) customerService).setUnmarshaller(this.customerMarshaller());
        return customerService;
    }

    @Bean
    protected SkillSetService skillSetService() {
        SkillSetService skillSetService = new SkillSetServiceImpl();
        ((SkillSetServiceImpl) skillSetService).setMarshaller(this.skillMarshaller());
        ((SkillSetServiceImpl) skillSetService).setUnmarshaller(this.skillMarshaller());
        return skillSetService;
    }
}