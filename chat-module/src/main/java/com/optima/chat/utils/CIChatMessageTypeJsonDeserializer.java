package com.optima.chat.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.optima.chat.wsdl.comms.CIChatMessageType;

import java.io.IOException;

public class CIChatMessageTypeJsonDeserializer extends JsonDeserializer<CIChatMessageType> {

    @Override
    public CIChatMessageType deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        return Enum.valueOf(CIChatMessageType.class, p.getText());
    }
}
