package com.optima.security.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Component
public class LoggingFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(LoggingFilter.class);

    private List<String> protectedPaths = Arrays.asList("/api/android/", "/internal/addNewCustomer", "/internal/changeEmail","/internal/authenticateUser");

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filterChain)
            throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        // Ottieni l'IP del chiamante
        String clientIp = request.getRemoteAddr();
        // Log dell'IP
        //logger.info("Request from IP: {}", clientIp);
        // Ottieni l'IP del chiamante
        String clientIpxForwarded = getClientIp(request);
        // Log dell'IP
        //logger.info("Request from IP X-Forwarded-For: {}", clientIpxForwarded);

        // Verifica se il path della richiesta è protetto
        boolean isProtectedPath = protectedPaths.stream().anyMatch(requestURI::startsWith);
        if (isProtectedPath) {
            // Controlla se l'IP è autorizzato
            if (!isAuthorizedIp(clientIpxForwarded)) {
                logger.info("autorizzazione negata: {}", clientIpxForwarded);
                ((HttpServletResponse) response).sendError(HttpServletResponse.SC_FORBIDDEN, "Access Denied");
                return;
            }
        }

        // Continua con il filtro
        filterChain.doFilter(request, response);
    }


    private String getClientIp(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader != null && !xForwardedForHeader.isEmpty()) {
            // L'intestazione può contenere più IP separati da virgole
            return xForwardedForHeader.split(",")[0].trim();
        }
        return request.getRemoteAddr(); // Se non ci sono intestazioni, prendi l'IP remoto
    }


    private boolean isAuthorizedIp(String ip) {
        return ip.matches("^(172\\.(16|30)\\.\\d+\\.\\d+)$");
    }
}

