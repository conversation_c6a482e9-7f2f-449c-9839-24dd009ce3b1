package com.optima.security.processors.impl;

import com.optima.security.constants.TokenType;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.exceptions.TooManyCallsException;
import com.optima.security.model.JwtToken;
import com.optima.security.processors.TokenService;
import com.optima.security.repository.JwtTokenRepository;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static java.util.Collections.emptyList;


public class DisposableTokenServiceImpl implements TokenService {

    private final JwtTokenRepository jwtTokenRepository;

    public DisposableTokenServiceImpl(JwtTokenRepository jwtTokenRepository) {
        this.jwtTokenRepository = jwtTokenRepository;
    }

    @Value("${optima.security.token.disposable.tokens-per-user:5}")
    private Integer maxTokensPerClient;

    @Value("${optima.security.token.disposable.expiration-time:#{60*10*1000}}")
    private Long tokenExpirationTime;

    @Value("${optima.security.token.secret}")
    private String tokenSecret;

    @Override
    @Transactional
    public AbstractAuthenticationToken process(Claims claims) {
        if (claims != null) {
            jwtTokenRepository.deleteExpiredTokens();
            Optional<JwtToken> jwtToken = this.jwtTokenRepository.findById(claims.getSubject());
            if (jwtToken.isPresent()) {
                jwtTokenRepository.delete(jwtToken.get());
                return new UsernamePasswordAuthenticationToken(jwtToken.get(), null, emptyList());
            }
        }
        return null;
    }

    @Override
    public TokenType getProcessorType() {
        return TokenType.DISPOSABLE;
    }

    @Override
    public String buildToken(Object o) throws JwtTokenException {
        if (!(o instanceof Long)) {
            throw new RuntimeException("User id is required");
        }
        jwtTokenRepository.deleteExpiredTokens();
        List<JwtToken> tokenList = jwtTokenRepository.findAllByClientId((Long) o);
        if (tokenList != null && tokenList.size() > this.maxTokensPerClient) {
            throw new TooManyCallsException();
        }

        Date expiration = new Date(System.currentTimeMillis() + this.tokenExpirationTime);
        String uid = UUID.randomUUID().toString();
        String token = Jwts.builder()
                .setSubject(uid)
                .claim("tokenType", TokenType.DISPOSABLE)
                .setExpiration(expiration)
                .signWith(SignatureAlgorithm.HS256, this.tokenSecret)
                .compact();
        JwtToken jwtToken = new JwtToken();
        jwtToken.setUid(uid);
        jwtToken.setExpirationTime(expiration);
        jwtToken.setTokenType(TokenType.DISPOSABLE);
        jwtToken.setToken(token);
        jwtToken.setClientId((Long) o);
        jwtTokenRepository.save(jwtToken);
        return token;
    }

    @Override
    public String buildToken(Object o, String userId) throws JwtTokenException {
        return null;
    }
}
