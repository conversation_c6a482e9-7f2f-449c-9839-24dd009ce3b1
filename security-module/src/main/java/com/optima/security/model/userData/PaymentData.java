package com.optima.security.model.userData;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;


@Data
public class PaymentData {

    @JsonProperty("modalitaPagamento")
    private String modalita;

    private String iban;

    private String intestatario;

    private String nomeSottoscrittore;

    private String cognomeSottoscrittore;

    private String cf;

    private String brand;

    private String maskedPan;

    private String expireMonth;

    private String expireYear;

    private String dataRichiestaAttivazione;


    public String getModalita() {
        return modalita;
    }

    public void setModalita(String modalita) {
        this.modalita = modalita;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getIntestatario() {
        return intestatario;
    }

    public void setIntestatario(String intestatario) {
        this.intestatario = intestatario;
    }

    public String getNomeSottoscrittore() {
        return nomeSottoscrittore;
    }

    public void setNomeSottoscrittore(String nomeSottoscrittore) {
        this.nomeSottoscrittore = nomeSottoscrittore;
    }

    public String getCognomeSottoscrittore() {
        return cognomeSottoscrittore;
    }

    public void setCognomeSottoscrittore(String cognomeSottoscrittore) {
        this.cognomeSottoscrittore = cognomeSottoscrittore;
    }

    public String getCf() {
        return cf;
    }

    public void setCf(String cf) {
        this.cf = cf;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMaskedPan() {
        return maskedPan;
    }

    public void setMaskedPan(String maskedPan) {
        this.maskedPan = maskedPan;
    }

    public String getExpireMonth() {
        return expireMonth;
    }

    public void setExpireMonth(String expireMonth) {
        this.expireMonth = expireMonth;
    }

    public String getExpireYear() {
        return expireYear;
    }

    public void setExpireYear(String expireYear) {
        this.expireYear = expireYear;
    }

    public String getDataRichiestaAttivazione() {
        return dataRichiestaAttivazione;
    }

    public void setDataRichiestaAttivazione(String dataRichiestaAttivazione) {
        this.dataRichiestaAttivazione = dataRichiestaAttivazione;
    }
}
