import { Component, Input } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-form-input',
  templateUrl: './form-input.component.html',
  styleUrls: ['./form-input.component.scss']
})
export class FormInputComponent {

  @Input('name') name: string;

  @Input('placeholder') placeholder = '';

  group: FormGroup;

  formControl: FormControl;

  @Input('group')
  set formGroup(formGroup: FormGroup) {
    if (formGroup) {
      this.group = formGroup;
      this.formControl = <FormControl>formGroup.controls[this.name];
    }
  }

}
