import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import * as Chart from 'chart.js';
import ChartConfig from '../../utils/chart-builder/ChartConfig';

@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  // styleUrls: ['./chart.component.scss']
})
export class ChartComponent implements OnInit {

  public chart: any;
  @ViewChild('myChart') context: ElementRef;

  constructor() {
  }


  buildChart(config: ChartConfig) {
    if (this.chart) {
      this.chart.destroy();
    }
    this.chart = new Chart(this.context.nativeElement, config);
  }

  @Input('config')
  set config(config: ChartConfig) {
    if (config) {
      this.buildChart(config);
    }
  }

  ngOnInit() {

  }
}
