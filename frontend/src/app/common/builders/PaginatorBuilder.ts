import { Paginator } from '../utils/pagination/Paginator';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { PaginationCallback } from '../utils/pagination/PaginationCallback';
import DefaultPagination from '../utils/pagination/DefaultPagination';
import YearlyPagination from '../utils/pagination/YearlyPagination';

export class PaginatorBuilder {

  private array: Paginator['array'];
  private onChange: Paginator['onChange'];
  private page = 1;
  private step: Paginator['step'];
  private reverse: boolean;
  private paginationCallback: PaginationCallback;

  static builder(): PaginatorBuilder {
    return new this();
  }

  forArray(arg: Paginator['array']): PaginatorBuilder {
    this.array = arg;
    return this;
  }

  withOnChange(arg: Paginator['onChange']): PaginatorBuilder {
    this.onChange = arg;
    return this;
  }

  startPage(arg: number = 1): PaginatorBuilder {
    this.page = arg;
    return this;
  }

  yearlyPagination(): PaginatorBuilder {
    this.paginationCallback = new YearlyPagination();
    return this;
  }

  withStep(arg: Paginator['step'] = 3): PaginatorBuilder {
    this.step = arg;
    return this;
  }

  reversive(): PaginatorBuilder {
    this.reverse = true;
    return this;
  }

  build(): Paginator {
    const {paginationCallback, array, onChange, page, step, reverse} = this;
    if (array && onChange) {
      const paginator = new Paginator();
      paginator.array = array;
      paginator.onChange = onChange;
      paginator.page = new BehaviorSubject(page);
      paginator.step = step;
      paginator.reverse = reverse;
      paginator.paginationCallback = paginationCallback ? paginationCallback : new DefaultPagination();
      paginator.initialize();
      return paginator;
    }
  }


}
