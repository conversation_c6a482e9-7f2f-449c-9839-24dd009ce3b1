import {InternetType} from '../enum/InternetType';
import {InternetProfile} from '../enum/InternetProfile';
import {ADSLType} from '../enum/ADSLType';

/*export const profileTypeDecoder = {
  [InternetProfile.OPTIMA_WEB_POP]: InternetType.ADSL_7,
  [InternetProfile.OPTIMA_WEB_JAZ]: InternetType.ADSL_20,
  [InternetProfile.OPTIMA_WEB_ROCK]: InternetType.ADSL_20,
  [InternetProfile.OPTIMA_WEB_640_POP]: InternetType.ADSL,
  [InternetProfile.FFIBRA_100_MPI]: InternetType.INTERNET_VELOCE,
  [InternetProfile.BITSTREAM_20]: InternetType.ADSL_7,
  [InternetProfile.BITSTREAM_7]: InternetType.ADSL_20,
  [InternetProfile.BITSTREAM_640]: InternetType.ADSL,
  [InternetProfile.ADSL_20]: InternetType.ADSL_20,
  [InternetProfile.ADSL_7]: InternetType.ADSL_7,
  [InternetProfile.VDSL_FAST_100]: InternetType.INTERNET_VELOCE
};*/

export const internetTypeDecoder = {
  [InternetProfile.VDSL_2]: ADSLType.INTERNET_VELOCE,
  [InternetProfile.BITSTREAM_20]: ADSLType.ADSL,
  [InternetProfile.ADSL_20]: ADSLType.ADSL,
  [InternetProfile.ADSL_20_MB]: ADSLType.ADSL,
  [InternetProfile.VDSL_FAST_100]: ADSLType.INTERNET_VELOCE,
  [InternetProfile.OPTIMA_SPRING]: ADSLType.ADSL,
  [InternetProfile.FTTH]: ADSLType.FIBRA,
  [InternetProfile.FTTH_CONSUMER]: ADSLType.FIBRA
};

/*export const decodeInternetProfile = (internetProfile: string | InternetProfile) => {
  const result = profileTypeDecoder[internetProfile];
  return result ? result : internetProfile;
};*/

export const decodeInternetType = (internetProfile: string | InternetProfile) => {
  return internetTypeDecoder[internetProfile] ? internetTypeDecoder[internetProfile] : internetProfile;
};
