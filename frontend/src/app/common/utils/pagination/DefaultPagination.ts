import { PaginationCallback } from './PaginationCallback';
import { Paginator } from './Paginator';

export default class DefaultPagination implements PaginationCallback {
  public paginationCallback(context: Paginator): any {
    context.pages = Math.ceil(context.array.length / context.step);
    return (nextPage) => {
      context.hasNextHasPrevious(nextPage);
      let from;
      let to;
      if (context.reverse) {
        from = context.array.length - context.step * nextPage;
        to = from + context.step;
      } else {
        from = (nextPage * context.step) - context.step;
        to = from + context.step;
      }
      context.onChange(context.array.slice(Math.max(0, from), to));
    };
  }

  nextValue(context): any {
  }

  previousValue(context): any {
  }

}
