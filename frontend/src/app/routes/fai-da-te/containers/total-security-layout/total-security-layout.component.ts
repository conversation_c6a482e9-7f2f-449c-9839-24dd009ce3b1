import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {Subscription} from 'rxjs/Subscription';
import {Utility} from '../../../../common/model/services/userServices.model';

@Component({
  selector: 'app-total-security-layout',
  templateUrl: './total-security-layout.component.html',
  styleUrls: ['./total-security-layout.component.scss']
})
export class TotalSecurityLayoutComponent implements OnInit, OnDestroy {

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  serviceDataSubscription: Subscription;
  totalSecurityInfo: Utility;

  constructor() {
    this.serviceDataSubscription = this.serviceData.subscribe(serviceState => {
      serviceState.services.forEach(service => {
        if (service.serviceName === 'TotalSecurity') {
          this.totalSecurityInfo = service.utilities[0];
        }
      });
    });
  }

  ngOnInit() {
  }

  ngOnDestroy(): void {
    if (this.serviceDataSubscription) {
      this.serviceDataSubscription.unsubscribe();
    }
  }
}
