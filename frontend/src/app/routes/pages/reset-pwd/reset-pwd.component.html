<section class="navBar">
<div class="col-xs-8 col-sm-6 col-md-4 col-lg-4">
  <div class="logo col-xs-8 col-sm-8 col-md-8 col-lg-8" routerLink="/login">

  </div>
</div>
  <div class="wrapper">
    <div class="mt-xl containerBlock center-block">
  <section id="form">

    <div class="mainBlock">
      <header>
        <b>Aggiorna password</b>
      </header>

      <div #text class="col-xs-12 col-sm-12 col-md-12 col-lg-12 text" >
        Inserisci la mail del tuo account Optima e ti invieremo le istruzioni per creare la tua password o aggiornarla se l'hai già creata.
      </div>

      <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <form [formGroup]="valForm" class="form-validate mb-lg inputs-group" role="form" name="loginForm" novalidate=""
              (submit)="submitForm($event, valForm.value)" #form>

          <div class="input-section">
           <!-- <label for="inputEmail">Inserisci l'e-mail indicata in contratto</label>-->
            <input class="form-control" id="inputEmail" type="text" name="email" placeholder="E-mail" autocomplete="off" formControlName="email" required="" />
            <span class="text-danger" *ngIf="valForm.controls['email'].hasError('required') && (valForm.controls['email'].dirty || valForm.controls['email'].touched)">Campo obbligatorio</span>
          </div>

          <div class="form-group has-feedback captcha-block" (click)="hideError()">
            <div class="g-recaptcha" data-sitekey="6LcxUnMUAAAAANcgBiEjajux7-jABwUv-O_KTUWB" (click)="hideError()"></div>
          <div *ngIf="captchaError" class="text-danger position">Recaptcha non verificato.</div>
          </div>

            <button class="submit-button" type="submit"> PROSEGUI
              <!--            <span class= "submitArrow">
                          <i class="fa fa-arrow-right fa-2x" aria-hidden="true"> </i>
                          </span>-->
              <!--<span class="prosegui">prosegui</span>-->
            </button>

        </form>
        <div #success class="hide">
          <div class="successText">
          Un messaggio di recupero password è stato inviato alla tua email!
          </div>
         <!-- <button class="submit mt-lg" routerLink="/login">
            <span class= "submitSuccess">
            <i class="fa fa-arrow-right fa-2x" aria-hidden="true"> </i>
            </span>
            &lt;!&ndash;<span class="prosegui">prosegui</span>&ndash;&gt;
          </button>-->
        </div>
        <div #error class="hide">
          Oops!Qualcosa è andato storto, riprova!
        </div>
      </div>
    </div>
  </section>

    </div>
  </div>
</section>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>

<div class="modal-div display" *ngIf="noSuchEmail">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="modal-image" src="assets/img/icons/Alert.png" alt="Alert">
    <div class="modal-text">L’indirizzo e-mail inserito non è associato a nessuna anagrafica.</div>
  </div>
</div>
