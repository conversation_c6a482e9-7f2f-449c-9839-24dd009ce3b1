@import "../../../../shared/styles/colors";
@import "../../../../shared/styles/app.mat-dropdown";

.titleBlock {
  clear: both;
  text-align: center;

  .title {
    color: $dark-blue;
  }

  .icon {
    position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_Telefono.png") no-repeat;
    width: 80px;
    background-size: contain;
    top: -10px;
    left: 10px;

  }

  margin-bottom: 20px;
  border: 2px solid $menu-border;
  border-radius: 5px;
  padding: 15px;
}

.block {
  border-radius: 5px;
  border: 1px solid $menu-border;
  background-color: white;
  height: auto;
  padding-top: 15px;
  padding-bottom: 15px;
  color: $dark-blue;
  min-height: 200px;
  overflow-y: auto;
}

.mobileView {
  display: none;
}

.blockWithDetail {
  border-radius: 5px 0 0 5px;
  border: 1px solid $menu-border;
  background-color: white;
  min-height: 200px;
  color: $dark-blue;
}

.detail {
  border-radius: 0 5px 5px 0;
  background: $menu-background;
  padding: 15px;
  min-height: 200px;

  span {
    display: block;
  }
}

.noActive {
  color: #b6cce3;
}

.subBlock {
  color: $dark-blue;

  .title {
    color: $dark-blue;
    font-weight: bold;
    font-size: 18px;
  }
}


.lineBlock {
  border-bottom: 1px solid $menu-border;
}

.bigLetter {
  margin-bottom: 10px;
  font-size: 16px;
}

.smallLetter {
  font-size: 14px;
}

.plainBlock {
  font-weight: 500;
  padding: 10px 0;
  font-size: 14px;
  line-height: 15px;
  position: relative;

  > p {
    padding-right: 110px;
    margin-bottom: 5px;
  }
}

.button {
  position: absolute;
  right: 15px;
  top: 10px;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 2px 15px;
  border-radius: 5px;
  font-size: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &.active {
    border: 1px solid #cccccc;
    color: #cccccc;
    background-color: #ffffff;
  }
}

.nascondi {
  background: white;
}

.visual {
  display: inline-block;
  background: white;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 2px 5px;
  border-radius: 5px;
  font-size: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-top: 10px;
}

.button:hover {
  background: #f0f5f9;
}

.subBlockWithDetails {
  padding: 10px 0;

  .title {
    color: #e54c36;
    font-weight: bold;
    font-size: 20px;
  }
}

.contentBlock {
  padding: 0;
}

.icons {
  margin-top: 10px;
}

.app--btn-dropdown {
  padding-right: 0 !important;
  padding-left: 0 !important;
  border: none;
  background: none;
}

.icon {
  width: 65px;
  height: 65px;
  margin: 10px auto 0 auto;
  display: inline-block;

}

.modifica {
  background: url("../../../../../assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

button a {
  margin-left: 15px;
}

.mobileView {
  display: none;
}

.mat-menu-style {
  width: 450px;
}

@media only screen and (max-width: 991px) {
  .lineBlock {
    border-bottom: none !important;
  }
  .blockWithDetail {
    background-color: $menu-background;
  }
  .lineBlock:nth-child(1) {
    .plainBlock {
      border-top: 1px solid $menu-border;
    }
  }
  .plainBlock {
    padding: 10px 15px;
  }
  .titleBlock {
    display: none;
  }
  .mobileView {
    display: inline-block;
  }
  .subBlockWithDetails {
    border-bottom: 1px solid $menu-border;
  }
  .detail {
    min-height: 10px;
    border-radius: 0;
    padding: 10px 15px;
    position: absolute;
    width: 90%;
  }
  .col-md-9 {
    padding: 0;
  }
  .icon {
    float: left;
    width: 65px;
    height: 65px;
    margin: 10px 15px 0 auto;
  }
  .icons {
    margin-left: 0;
    margin-top: 0;
    height: 190px;
    padding-left: 90%;
  }
  .url {
    margin-top: 15px;

    a {
      padding: 8px 0;
      color: $dark-blue;
    }

    a:hover {
      text-decoration: underline;
    }
  }
  .modificaPDF {
    clear: both;
    display: none;
  }
  .show {
    display: block;
  }
  .pdflist {
    line-height: 15px;
    padding: 5px 0;

    a {
      color: $dark-blue;
      text-decoration: none;
    }
  }
  .block {
    border: none;
  }
  .detailBlock {
    padding-top: 10px;
  }

  .button {
    top: 10px;
    padding: 8px 15px;
  }

  .modifica {
    margin-top: 10px;
    margin-right: 15px;
  }
  .icon-selection {
    display: inline-block;
  }
  .app--btn-dropdown {
    padding-bottom: 0;
    padding-top: 0;
  }
}

@media only screen and (max-width: 768px) {
  .detail {
    min-height: 10px;
    border-radius: 0;
    padding: 10px 15px;
    position: initial;
    width: 100%;
  }
  .icons {
    width: 280px;
    margin: 30px auto 0;
    padding-right: 0;
    padding-left: 0;
    height: 100px;
  }
  .app--btn-dropdown {
    padding-right: 0 !important;
    padding-left: 0 !important;
    border: none;
    background: none;
    height: 35px;
  }

  .icon-selection {
    /*padding-top: 0;
    margin-bottom: 0;*/
    margin-bottom: 0;
  }

  .icon {
  }
  .modifica {
    background: white none;
    border: 1px solid $dark-blue;
    font-size: 18px;
    alignment: center;
    border-radius: 2px;
    text-align: center;
    align-content: center;
    margin-top: 0;
    height: 38px;
    width: 280px;
    position: absolute;
    padding-top: 5px;
    margin-right: 0;
  }
  .modifica:after {
    font-family: Lato-Regular, serif;
    font-style: normal;
    color: $dark-blue;
    content: "Modifica";
  }

  ::ng-deep.mat-menu-panel {
    margin-right: 0;
    margin-top: 34px;
  }
}
