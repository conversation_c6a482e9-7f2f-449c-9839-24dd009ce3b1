import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {UserData} from '../../../../common/model/userData.model';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import {Subscription} from 'rxjs/Subscription';
import {Utility} from '../../../../common/model/services/userServices.model';
import {FissoType} from '../../../../common/enum/FissoType';
import {ServiceStatus} from '../../../../common/enum/ServiceStatus';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {ServicesActions} from '../../../../redux/services/actions';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {HomeService} from '../home/<USER>';

@Component({
  selector: 'app-fisso-layout',
  templateUrl: './fisso-layout.component.html',
  styleUrls: ['./fisso-layout.component.scss']
})
export class FissoLayoutComponent implements OnInit, OnDestroy {
  @select(['services'])
  services: Observable<ServiceStateModel>;
  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  checkid: number;
  sortedServices: Array<any>;
  userCluster: string;
  pdf: Array<any>;

  detail: Utility;

  podDetails = {};

  serviceSubscription: Subscription;
  userInfoSubscription: Subscription;

  constructor(private serviceActions: ServicesActions, private homeServices: HomeService) {
    this.userInfoSubscription = this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.serviceSubscription = this.services.subscribe(userServices => {
      const {servicesLoaded, fissoPodDetailsLoading, fissoPodDetailsLoaded, fissoPodDetails, services} = userServices;
      this.podDetails = fissoPodDetails;
      this.sortedServices = services.filter(service => (service.serviceName === FissoType.VOCE ||
        service.serviceName === FissoType.VOIP ||
        service.serviceName === FissoType.WLR)).map(service => {
        service.utilities = service.utilities.filter(utility => utility.status === ServiceStatus.ATTIVATO
          || utility.status === ServiceStatus.IN_ATTIVAZIONE);
        return service;
      });
      if (servicesLoaded && !fissoPodDetailsLoaded && !fissoPodDetailsLoading) {
        this.serviceActions.loadFissoPodDetails(localStorage.getItem('clientId'));
      }
    });
  }

  selectValue(utility: Utility, i) {
    this.checkid = i;
    this.pdf = this.setPDFList(FissoType.VOCE);
    this.detail = utility;
  }

  hide() {
    this.detail = null;
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.serviceSubscription, this.userInfoSubscription]);
  }
}
