package com.optima.common.models.wrappers.sms;

import java.util.ArrayList;
import java.util.List;


public class SMSEntity {
    private String textMessage;
    private List<Destination> destinations;

    public SMSEntity(String textMessage, String... numbers) {
        destinations = new ArrayList<>();
        for (String number : numbers) {
            destinations.add(new Destination(number));
        }
        this.textMessage = textMessage;
    }

    public List<Destination> getDestinations() {
        return destinations;
    }

    public void setDestinations(List<Destination> destinations) {
        this.destinations = destinations;
    }

    public String getTextMessage() {
        return textMessage;
    }

    public void setTextMessage(String textMessage) {
        this.textMessage = textMessage;
    }
}